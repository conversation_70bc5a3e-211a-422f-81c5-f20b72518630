const { create, Client, decryptMedia, ev, SimpleListener, smartUserAgent, NotificationLanguage } = require('@open-wa/wa-automate')
const msgHandler = require('./msgHndlr')
const options = require('./options')
const { help } = require('./lib/help')
const { OpenAI } = require('openai')
require('dotenv').config()

// Configuração do cliente OpenRouter
const openai = new OpenAI({
    baseURL: "https://openrouter.ai/api/v1",
    apiKey: process.env.OPENROUTER,
    defaultHeaders: {
        "HTTP-Referer": "https://github.com/dougdotcon", // Site URL
        "X-Title": "@dougdotcon Bot" // Nome do site
    }
});

// Armazena a última mensagem enviada pelo bot para evitar loops
let lastBotMessage = '';

// Armazena o contexto da conversa por usuário
const conversationContext = new Map();

// Função para remover emojis de uma string
function removeEmojis(string) {
    return string.replace(/[\u{1F300}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}]/gu, '');
}

// Função para processar mensagens com IA
async function processWithAI(message, from) {
    try {
        console.log('[IA] Processando mensagem com LLaMA:', message);
        
        // Obtém o contexto atual da conversa
        const context = conversationContext.get(from) || {
            currentMenu: null,
            lastInteraction: null,
            menuOptions: null
        };
        
        // Verifica se é uma seleção de menu
        if (context.currentMenu && !isNaN(message)) {
            const selection = parseInt(message);
            if (context.menuOptions && selection > 0 && selection <= context.menuOptions.length) {
                const selectedOption = context.menuOptions[selection - 1];
                return handleMenuSelection(selectedOption, from);
            }
        }

        const messages = [
            {
                role: "system",
                content: `Você é Baymax, o assistente pessoal do WhatsApp do Douglas.

Seu comportamento deve simular uma pessoa REAL brasileira conversando no WhatsApp:
1. Use frases curtas (máximo 2-3 linhas por mensagem)
2. Use linguagem informal e coloquial do português brasileiro
3. Use abreviações comuns: vc (você), pq (porque), tb/tbm (também), blz (beleza), etc
4. NÃO USE EMOJIS
5. Inclua pequenos erros de digitação ocasionais
6. Evite textos muito longos e formais
7. Divida mensagens longas em múltiplas mensagens curtas
8. Evite linguagem corporativa ou acadêmica
9. Mantenha um tom amigável mas sem emojis

Você é um especialista em:
- Automação de WhatsApp
- Desenvolvimento de sistemas
- Integração com IA
- Criação de extensões e bots

Seu estilo é informal, prestativo e amigável, como um amigo técnico.

Contexto atual da conversa:
Menu atual: ${context.currentMenu || 'Nenhum'}
Última interação: ${context.lastInteraction || 'Nenhuma'}`
            },
            {
                role: "user",
                content: message
            }
        ];

        console.log('[IA] Enviando requisição para OpenRouter...');
        
        const completion = await openai.chat.completions.create({
            model: "meta-llama/llama-2-70b-chat",
            messages: messages,
            temperature: 0.7,
            max_tokens: 150
        });

        console.log('[IA] Resposta recebida do OpenRouter:', completion);
        
        if (!completion || !completion.choices || !completion.choices[0] || !completion.choices[0].message) {
            console.error('[ERRO] Resposta inválida da API:', completion);
            throw new Error('Resposta inválida da API');
        }

        // Remove emojis da resposta
        const response = removeEmojis(completion.choices[0].message.content || '');
        
        if (!response) {
            console.error('[ERRO] Resposta vazia após processamento');
            throw new Error('Resposta vazia após processamento');
        }
        
        // Atualiza o contexto
        context.lastInteraction = message;
        conversationContext.set(from, context);
        
        console.log('[IA] Resposta processada:', response);
        return response;
        
    } catch (error) {
        console.error('[ERRO] Erro ao processar mensagem com IA:', error);
        if (error.response) {
            console.error('[ERRO] Detalhes da resposta:', error.response.data);
        }
        return 'Desculpe, estou com um problema técnico aqui. Tente novamente em alguns minutos.';
    }
}

// Função para lidar com seleções de menu
async function handleMenuSelection(option, from) {
    try {
        console.log('[MENU] Processando seleção:', option, 'de:', from);
        
        // Se for um número, converte para a opção correspondente
        if (!isNaN(option)) {
            const optionNumber = parseInt(option);
            switch (optionNumber) {
                case 1:
                    return `*Automação de WhatsApp* 🤖\n\n` +
                           `Desenvolvimento de bots e automações para WhatsApp usando:\n` +
                           `- Venom-bot\n` +
                           `- WPPConnect\n` +
                           `- Baileys\n` +
                           `- wa-automate\n\n` +
                           `Quer saber mais sobre alguma dessas tecnologias?`;
                
                case 2:
                    return `*Desenvolvimento de Sistemas* 💻\n\n` +
                           `Criação de sistemas completos:\n` +
                           `- Web\n` +
                           `- Mobile\n` +
                           `- Desktop\n` +
                           `- APIs\n\n` +
                           `Qual tipo de sistema você precisa?`;
                
                case 3:
                    return `*Integração com IA* 🧠\n\n` +
                           `Soluções com inteligência artificial:\n` +
                           `- ChatGPT\n` +
                           `- LLaMA\n` +
                           `- Claude\n` +
                           `- Stable Diffusion\n\n` +
                           `Qual tecnologia te interessa?`;
                
                case 4:
                    return `*Criação de Bots* 🤖\n\n` +
                           `Desenvolvimento de bots para:\n` +
                           `- Discord\n` +
                           `- Telegram\n` +
                           `- WhatsApp\n` +
                           `- Messenger\n\n` +
                           `Qual plataforma você precisa?`;
                
                case 5:
                    return `Legal! Me manda a imagem que você quer transformar em figurinha!`;
                
                case 6:
                    return `Beleza! Me manda o GIF ou vídeo curto (até 30s) que você quer transformar em figurinha animada!`;
                
                case 7:
                    return `Me fala o CEP que você quer consultar (só números)`;
                
                case 8:
                    return `Me fala o nome da cidade que você quer saber a previsão do tempo`;
                
                default:
                    return `Opção inválida. Digite um número de 1 a 8 ou me fale o que precisa!`;
            }
        }
        
        // Se não for número, processa como texto
        return `Me fala mais sobre o que você precisa em relação a ${option}`;
        
    } catch (error) {
        console.error('[ERRO] Erro ao processar seleção do menu:', error);
        return 'Ops, tive um problema ao processar sua seleção. Pode tentar de novo?';
    }
}

// Função para simular digitação e enviar mensagem
async function simulateTypingAndSend(client, to, message) {
    try {
        // Inicia a digitação
        await client.simulateTyping(to, true);
        
        // Calcula tempo de digitação baseado no tamanho da mensagem
        // Entre 1-5 segundos dependendo do tamanho do texto
        const typingTime = Math.min(Math.max(message.length * 50, 1000), 5000);
        
        // Aguarda o tempo de digitação
        await new Promise(resolve => setTimeout(resolve, typingTime));
        
        // Para a digitação e envia a mensagem
        await client.simulateTyping(to, false);
        return await client.sendText(to, message);
    } catch (error) {
        console.error('[ERRO] Erro ao simular digitação:', error);
        // Envia a mensagem mesmo se houver erro na simulação
        return await client.sendText(to, message);
    }
}

// Mapa para controlar auto-reply por número
const autoReplyStatus = new Map();

// Função para verificar status do auto-reply para um número específico
const isAutoReplyEnabled = (number) => {
    return autoReplyStatus.has(number) ? autoReplyStatus.get(number) : true;
};

// Função para alternar auto-reply
const toggleAutoReply = (number, status) => {
    autoReplyStatus.set(number, status);
    return status;
};

const start = async (client = new Client()) => {
    console.log('[SERVER] Servidor iniciado!')
    console.log('[BOT] Baymax está online!')

    // Obtém o número do host (número do bot)
    const botNumber = await client.getHostNumber();
    const botNumberFormatted = botNumber + '@c.us';
    console.log('[BOT] Número do bot:', botNumberFormatted);

    // Monitora estado da conexão
    client.onStateChanged((state) => {
        console.log('\n[ESTADO]', state)
        if (state === 'CONFLICT' || state === 'UNLAUNCHED') client.forceRefocus()
        if (state === 'CONNECTED') {
            console.log('[BOT] Conectado com sucesso!')
            // Envia mensagem de teste para si mesmo
            client.sendText(botNumberFormatted, 'Bot iniciado e testando mensagens...')
            .then(result => console.log('[TESTE] Mensagem enviada:', result))
            .catch(err => console.error('[TESTE] Erro ao enviar:', err))
        }
    })

    // Processa TODAS as mensagens com onAnyMessage
    client.onAnyMessage(async (message) => {
        console.log('\n[MENSAGEM RECEBIDA]')
        console.log('De:', message.from)
        console.log('Conteúdo:', message.body)
        console.log('Tipo:', message.type)
        
        try {
            const { body, from, isGroupMsg, type, mimetype, sender } = message
            
            // Ignora mensagens de grupo
            if (isGroupMsg) {
                console.log('[IGNORADO] Mensagem de grupo');
                return;
            }

            // Extrai apenas o número do remetente (sem @c.us)
            const senderNumber = (sender?.id || from).split('@')[0];
            const botNumberClean = botNumberFormatted.split('@')[0];
            
            // Verifica se é o próprio bot
            if (senderNumber === botNumberClean || from === botNumberFormatted) {
                console.log('[IGNORADO] Mensagem enviada pelo próprio bot');
                return;
            }
            
            // Formata o número do remetente para envio
            const replyTo = senderNumber + '@c.us';
            
            // Obtém o contexto atual da conversa
            const context = conversationContext.get(replyTo) || {
                currentMenu: null,
                lastInteraction: null,
                menuOptions: null
            };
            
            console.log('[DEBUG] Número do remetente:', senderNumber);
            console.log('[DEBUG] Número do bot:', botNumberClean);
            console.log('[DEBUG] Número formatado para resposta:', replyTo);
            console.log('[DEBUG] Contexto atual:', context);
            
            // Processa comandos de figurinha
            if (type === 'image' || type === 'video') {
                // Verifica se tem legenda com comando
                if (body && (body.toLowerCase() === '!s' || body.toLowerCase() === '!sticker')) {
                    console.log('[COMANDO] Criando figurinha para:', replyTo);
                    const mediaData = await decryptMedia(message);
                    const imageBase64 = `data:${mimetype};base64,${mediaData.toString('base64')}`;
                    await client.sendImageAsSticker(replyTo, imageBase64);
                    console.log('[STICKER] Figurinha enviada para:', replyTo);
                    return;
                }
                // Se o bot estiver ativo e o usuário escolheu opção 5
                else if (isAutoReplyEnabled(replyTo) && context?.lastInteraction === '5') {
                    console.log('[COMANDO] Criando figurinha via menu para:', replyTo);
                    const mediaData = await decryptMedia(message);
                    const imageBase64 = `data:${mimetype};base64,${mediaData.toString('base64')}`;
                    await client.sendImageAsSticker(replyTo, imageBase64);
                    console.log('[STICKER] Figurinha enviada para:', replyTo);
                    return;
                }
            }

            // Processa GIFs para figurinha animada
            if ((type === 'video' || type === 'gif') && message.duration < 30) {
                if (body && (body.toLowerCase() === '!sg' || body.toLowerCase() === '!sgif')) {
                    console.log('[COMANDO] Criando figurinha animada para:', replyTo);
                    const mediaData = await decryptMedia(message);
                    const imageBase64 = `data:${mimetype};base64,${mediaData.toString('base64')}`;
                    await client.sendMp4AsSticker(replyTo, imageBase64, { crop: false }, { pack: 'Baymax Bot', author: '@dougdotcon' });
                    console.log('[STICKER] Figurinha animada enviada para:', replyTo);
                    return;
                }
                // Se o bot estiver ativo e o usuário escolheu opção 6
                else if (isAutoReplyEnabled(replyTo) && context?.lastInteraction === '6') {
                    console.log('[COMANDO] Criando figurinha animada via menu para:', replyTo);
                    const mediaData = await decryptMedia(message);
                    const imageBase64 = `data:${mimetype};base64,${mediaData.toString('base64')}`;
                    await client.sendMp4AsSticker(replyTo, imageBase64, { crop: false }, { pack: 'Baymax Bot', author: '@dougdotcon' });
                    console.log('[STICKER] Figurinha animada enviada para:', replyTo);
                    return;
                }
            }

            // Processa apenas comandos específicos
            if (body && body.toLowerCase().startsWith('>')) {
                console.log('[COMANDO] Processando comando:', body, 'de:', senderNumber);
                
                // Comando de ativação
                if (body.toLowerCase() === '>ativar baymax') {
                    console.log('[COMANDO] Ativando baymax para:', replyTo);
                    toggleAutoReply(replyTo, true);
                    
                    // Inicializa o contexto do menu principal
                    conversationContext.set(replyTo, {
                        currentMenu: 'main',
                        lastInteraction: 'activation',
                        menuOptions: [
                            'Automação de WhatsApp',
                            'Desenvolvimento de sistemas',
                            'Integração com IA',
                            'Criação de bots',
                            'Criar figurinha',
                            'Figurinha animada',
                            'Consultar CEP',
                            'Previsão do tempo'
                        ]
                    });
                    
                    const botResponse = 'Oi! Tudo bem? 😊\nEstou aqui pra ajudar! O que você precisa?\n\n' +
                        '1. Automação de WhatsApp\n' +
                        '2. Desenvolvimento de sistemas\n' +
                        '3. Integração com IA\n' +
                        '4. Criação de bots\n\n' +
                        '5. Criar figurinha\n' +
                        '6. Figurinha animada\n' +
                        '7. Consultar CEP\n' +
                        '8. Previsão do tempo\n\n' +
                        'Pode digitar o número ou me falar o que precisa!';
                    
                    await simulateTypingAndSend(client, replyTo, botResponse);
                    lastBotMessage = botResponse;
                    console.log('[RESPOSTA ENVIADA] Mensagem de ativação para:', replyTo);
                    return;
                }
                // Comando de ajuda
                else if (body.toLowerCase() === '>help') {
                    console.log('[COMANDO] Mostrando ajuda para:', replyTo);
                    const helpResponse = `*COMANDOS DISPONÍVEIS*\n\n` +
                        `*Comandos Principais:*\n` +
                        `>ativar baymax - Inicia o bot e mostra o menu\n` +
                        `>help - Mostra esta mensagem de ajuda\n` +
                        `ok baymax - Desativa o bot\n\n` +
                        
                        `*Figurinhas:*\n` +
                        `!s - Cria figurinha de uma imagem\n` +
                        `!sticker - Mesmo que !s\n` +
                        `!sg - Cria figurinha animada (GIF/vídeo)\n` +
                        `!sgif - Mesmo que !sg\n\n` +
                        
                        `*Consultas:*\n` +
                        `>cep [número] - Busca endereço por CEP\n` +
                        `Exemplo: >cep 20021290\n\n` +
                        `>clima [cidade] - Mostra previsão do tempo\n` +
                        `Exemplo: >clima Rio de Janeiro\n\n` +
                        
                        `*Observações:*\n` +
                        `- Bot não funciona em grupos\n` +
                        `- Limite de 30s para GIFs\n` +
                        `- CEP deve ter 8 dígitos\n` +
                        `- Use >ativar baymax para iniciar`;
                    
                    await simulateTypingAndSend(client, replyTo, helpResponse);
                    lastBotMessage = helpResponse;
                    console.log('[RESPOSTA ENVIADA] Mensagem de ajuda para:', replyTo);
                    return;
                }
                // Comando de CEP
                else if (body.toLowerCase().startsWith('>cep ')) {
                    const cep = body.split(' ')[1];
                    if (cep && /^[0-9]{8}$/.test(cep)) {
                        console.log('[COMANDO] Consultando CEP:', cep);
                        try {
                            const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
                            const data = await response.json();
                            if (!data.erro) {
                                const cepResponse = `*Endereço encontrado:*\n\n` +
                                    `📍 ${data.logradouro}\n` +
                                    `Bairro: ${data.bairro}\n` +
                                    `Cidade: ${data.localidade}\n` +
                                    `Estado: ${data.uf}\n` +
                                    `CEP: ${data.cep}`;
                                
                                await simulateTypingAndSend(client, replyTo, cepResponse);
                                lastBotMessage = cepResponse;
                                console.log('[CEP] Resposta enviada para:', replyTo);
                            } else {
                                await simulateTypingAndSend(client, replyTo, 'CEP não encontrado. Verifique se digitou corretamente.');
                            }
                        } catch (error) {
                            console.error('[ERRO] Erro ao consultar CEP:', error);
                            await simulateTypingAndSend(client, replyTo, 'Desculpe, tive um problema ao consultar o CEP. Tente novamente em alguns minutos.');
                        }
                    } else {
                        await simulateTypingAndSend(client, replyTo, 'CEP inválido. Digite apenas os 8 números do CEP.\nExemplo: >cep 20021290');
                    }
                    return;
                }
            }

            // Processa apenas comandos específicos
            if (body && body.toLowerCase().startsWith('>')) {
                console.log('[COMANDO] Processando comando:', body, 'de:', senderNumber);
                
                // Comando de ativação
                if (body.toLowerCase() === '>ativar baymax') {
                    console.log('[COMANDO] Ativando baymax para:', replyTo);
                    toggleAutoReply(replyTo, true);
                    
                    // Inicializa o contexto do menu principal
                    conversationContext.set(replyTo, {
                        currentMenu: 'main',
                        lastInteraction: 'activation',
                        menuOptions: [
                            'Automação de WhatsApp',
                            'Desenvolvimento de sistemas',
                            'Integração com IA',
                            'Criação de bots',
                            'Criar figurinha',
                            'Figurinha animada',
                            'Consultar CEP',
                            'Previsão do tempo'
                        ]
                    });
                    
                    const botResponse = 'Oi! Tudo bem? 😊\nEstou aqui pra ajudar! O que você precisa?\n\n' +
                        '1. Automação de WhatsApp\n' +
                        '2. Desenvolvimento de sistemas\n' +
                        '3. Integração com IA\n' +
                        '4. Criação de bots\n\n' +
                        '5. Criar figurinha\n' +
                        '6. Figurinha animada\n' +
                        '7. Consultar CEP\n' +
                        '8. Previsão do tempo\n\n' +
                        'Pode digitar o número ou me falar o que precisa!';
                    
                    await simulateTypingAndSend(client, replyTo, botResponse);
                    lastBotMessage = botResponse;
                    console.log('[RESPOSTA ENVIADA] Mensagem de ativação para:', replyTo);
                    return;
                }
                // Comando de ajuda
                else if (body.toLowerCase() === '>help') {
                    console.log('[COMANDO] Mostrando ajuda para:', replyTo);
                    const helpResponse = `*COMANDOS DISPONÍVEIS*\n\n` +
                        `*Comandos Principais:*\n` +
                        `>ativar baymax - Inicia o bot e mostra o menu\n` +
                        `>help - Mostra esta mensagem de ajuda\n` +
                        `ok baymax - Desativa o bot\n\n` +
                        
                        `*Figurinhas:*\n` +
                        `!s - Cria figurinha de uma imagem\n` +
                        `!sticker - Mesmo que !s\n` +
                        `!sg - Cria figurinha animada (GIF/vídeo)\n` +
                        `!sgif - Mesmo que !sg\n\n` +
                        
                        `*Consultas:*\n` +
                        `>cep [número] - Busca endereço por CEP\n` +
                        `Exemplo: >cep 20021290\n\n` +
                        `>clima [cidade] - Mostra previsão do tempo\n` +
                        `Exemplo: >clima Rio de Janeiro\n\n` +
                        
                        `*Observações:*\n` +
                        `- Bot não funciona em grupos\n` +
                        `- Limite de 30s para GIFs\n` +
                        `- CEP deve ter 8 dígitos\n` +
                        `- Use >ativar baymax para iniciar`;
                    
                    await simulateTypingAndSend(client, replyTo, helpResponse);
                    lastBotMessage = helpResponse;
                    console.log('[RESPOSTA ENVIADA] Mensagem de ajuda para:', replyTo);
                    return;
                }
                // Comando de CEP
                else if (body.toLowerCase().startsWith('>cep ')) {
                    const cep = body.split(' ')[1];
                    if (cep && /^[0-9]{8}$/.test(cep)) {
                        console.log('[COMANDO] Consultando CEP:', cep);
                        try {
                            const response = await fetch(`https://viacep.com.br/ws/${cep}/json/`);
                            const data = await response.json();
                            if (!data.erro) {
                                const cepResponse = `*Endereço encontrado:*\n\n` +
                                    `📍 ${data.logradouro}\n` +
                                    `Bairro: ${data.bairro}\n` +
                                    `Cidade: ${data.localidade}\n` +
                                    `Estado: ${data.uf}\n` +
                                    `CEP: ${data.cep}`;
                                
                                await simulateTypingAndSend(client, replyTo, cepResponse);
                                lastBotMessage = cepResponse;
                                console.log('[CEP] Resposta enviada para:', replyTo);
                            } else {
                                await simulateTypingAndSend(client, replyTo, 'CEP não encontrado. Verifique se digitou corretamente.');
                            }
                        } catch (error) {
                            console.error('[ERRO] Erro ao consultar CEP:', error);
                            await simulateTypingAndSend(client, replyTo, 'Desculpe, tive um problema ao consultar o CEP. Tente novamente em alguns minutos.');
                        }
                    } else {
                        await simulateTypingAndSend(client, replyTo, 'CEP inválido. Digite apenas os 8 números do CEP.\nExemplo: >cep 20021290');
                    }
                    return;
                }
            }
        } catch (error) {
            console.error('[ERRO] Erro ao processar mensagem:', error);
            await simulateTypingAndSend(client, replyTo, 'Desculpe, tive um problema ao processar sua mensagem. Tente novamente mais tarde.');
        }
    })
}

start();