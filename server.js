const express = require('express');
const { create } = require('venom-bot');
const cors = require('cors');
const fs = require('fs');
const path = require('path');
const QRCode = require('qrcode');
const qrcodeTerminal = require('qrcode-terminal');
const { OpenAI } = require('openai');
require('dotenv').config();

// Configuração do cliente OpenRouter
const openai = new OpenAI({
    baseURL: "https://openrouter.ai/api/v1",
    apiKey: process.env.OPENROUTER,
    defaultHeaders: {
        "HTTP-Referer": "https://github.com/dougdotcon", // Site URL
        "X-Title": "@dougdotcon Bot" // Nome do site
    }
});

const app = express();
app.use(express.json());
app.use(cors());

let client;
let qrCodeBase64 = null;

// Configurações do bot
const botConfig = {
    name: process.env.BOT_NAME || "Baymax",
    ownerNumber: process.env.OWNER_NUMBER || "5521982301476",
    autoReply: process.env.AUTO_REPLY === 'true',
    workingHoursStart: parseInt(process.env.WORKING_HOURS_START) || 8,
    workingHoursEnd: parseInt(process.env.WORKING_HOURS_END) || 22,
    ignoreGroups: process.env.IGNORE_GROUPS === 'true',
    responseDelay: parseInt(process.env.RESPONSE_DELAY) || 0,
    maxMessagesPerMinute: parseInt(process.env.MAX_MESSAGES_PER_MINUTE) || 30,
    bannedKeywords: (process.env.BANNED_KEYWORDS || '').split(','),
    allowedNumbers: (process.env.ALLOWED_NUMBERS || '').split(','),
    bannedNumbers: (process.env.BANNED_NUMBERS || '').split(','),
    debugMode: process.env.DEBUG_MODE === 'true',
    menuTimeout: parseInt(process.env.MENU_TIMEOUT) || 300000 // 5 minutos
};

// Mapa para controlar auto-reply por número
const autoReplyStatus = new Map();

// Função para verificar status do auto-reply para um número específico
const isAutoReplyEnabled = (number) => {
    return autoReplyStatus.has(number) ? autoReplyStatus.get(number) : botConfig.autoReply;
};

// Função para alternar auto-reply
const toggleAutoReply = (number, status) => {
    autoReplyStatus.set(number, status);
    return status;
};

// Função para processar mensagens com o OpenRouter AI
async function processWithAI(message, from, context = null) {
    try {
        const messages = [
            {
                role: "system",
                content: `Você é Baymax o assistente pessoal do Douglas, um desenvolvedor especializado em:
                - Automação de WhatsApp
                - Desenvolvimento de sistemas
                - Integração com IA
                - Criação de extensões e bots
                
                Seu estilo de comunicação é informal e amigável. Use linguagem coloquial do português brasileiro.
                Use emojis com moderação e mantenha as respostas concisas.
                
                ${context ? `Contexto atual:\nMenu: ${context.currentMenu}\nÚltima interação: ${context.lastInteraction}` : ''}`
            },
            {
                role: "user",
                content: message
            }
        ];

        const completion = await openai.chat.completions.create({
            model: "meta-llama/llama-4-maverick:free",
            messages: messages,
            temperature: 0.7,
            max_tokens: 150
        });

        return completion.choices[0].message.content;
    } catch (error) {
        console.error("❌ Erro ao processar com IA:", error);
        return "Desculpe, tive um problema ao processar sua mensagem. Pode tentar novamente?";
    }
}

// Função para processar seleções de menu
async function processMenuSelection(message, from, context) {
    // Verifica se é uma seleção numérica
    const selection = parseInt(message);
    if (!isNaN(selection)) {
        switch (context.currentMenu) {
            case 'main':
                if (selection >= 1 && selection <= 8) {
                    if (selection <= 4) {
                        // Opções de desenvolvimento
                        context.currentMenu = 'services';
                        return helpServices();
                    } else {
                        // Opções de ferramentas
                        switch (selection) {
                            case 5:
                            case 6:
                                return "Me manda a imagem que você quer transformar em figurinha! 😊";
                            case 7:
                                return "Me fala o CEP que você quer consultar 🔍";
                            case 8:
                                return "Qual cidade você quer saber a previsão do tempo? 🌤️";
                        }
                    }
                }
                break;
                
            case 'services':
                if (selection >= 1 && selection <= 4) {
                    const services = [
                        "Automação de WhatsApp",
                        "Desenvolvimento de Sistemas",
                        "Integração com IA",
                        "Criação de Bots"
                    ];
                    context.lastInteraction = services[selection - 1];
                    return `Legal! Você quer saber mais sobre ${services[selection - 1]}.\nO que você gostaria de saber especificamente? 🤓`;
                }
                break;
                
            case 'tech':
                if (selection >= 1 && selection <= 4) {
                    const techs = [
                        "Desenvolvimento Web",
                        "Automação",
                        "Inteligência Artificial",
                        "APIs e Integrações"
                    ];
                    context.lastInteraction = techs[selection - 1];
                    return helpTech();
                }
                break;
        }
    }
    
    // Se não for uma seleção válida, processa com IA
    return await processWithAI(message, from, context);
}

// Função para processar mensagens recebidas
const handleMessage = async (message, from, context) => {
    const text = message.toLowerCase();
    
    // Se estiver em um menu, tenta processar como seleção
    if (context && context.currentMenu) {
        return await processMenuSelection(text, from, context);
    }
    
    // Processa comandos comuns
    if (text.match(/^(oi|olá|ola|hey|hi)$/)) {
        return "Oi! Tudo bem? 😊\nPosso te ajudar com alguma coisa?";
    }
    
    if (text.match(/ajuda|help|menu/)) {
        return help();
    }
    
    // Se não for nenhum comando específico, processa com IA
    return await processWithAI(message, from, context);
};

// Função para baixar e salvar imagem
async function downloadImage(messageData) {
    try {
        if (messageData.isMedia && messageData.type === 'image') {
            const buffer = await client.decryptFile(messageData);
            const imagePath = path.join(__dirname, `temp_${Date.now()}.jpg`);
            fs.writeFileSync(imagePath, buffer);
            return imagePath;
        }
    } catch (error) {
        console.error("❌ Erro ao baixar imagem:", error);
    }
    return null;
}

// Perfil e serviços oferecidos
const profile = {
    name: "Baymax",
    services: [
        "Automação de WhatsApp",
        "Desenvolvimento de sistemas",
        "Integração com IA",
        "Criação de extensões e bots"
    ],
    interests: [
        "tecnologia",
        "criptomoedas",
        "filosofia",
        "IA"
    ],
    personality: {
        style: "humorístico e técnico",
        traits: ["curioso", "engajado", "reflexivo"]
    }
};

// Respostas automáticas baseadas em palavras-chave
const automaticResponses = {
    greeting: [
        "Olá! Sou o bot do @dougdotcon (+5521982301476). Como posso ajudar?",
        "Oi! Eu sou o assistente de desenvolvimento do Douglas. Posso te ajudar?"
    ],
    services: [
        "Ofereço serviços de:\n" +
        "🤖 Automação de WhatsApp\n" +
        "💻 Desenvolvimento de sistemas\n" +
        "🧠 Integração com IA\n" +
        "🔧 Criação de extensões e bots\n\n" +
        "Em qual desses serviços você tem interesse?"
    ],
    crypto: [
        "Sou entusiasta de criptomoedas e posso ajudar com análises técnicas e desenvolvimento de bots de trading!"
    ],
    technology: [
        "Adoro falar sobre tecnologia! Especialmente sobre IA, automação e desenvolvimento de software. Tem algum projeto em mente?"
    ],
    default: [
        "Interessante! Se quiser saber mais sobre meus serviços de automação e desenvolvimento, é só perguntar!"
    ]
};

// Função para processar mensagens recebidas
const processMessage = (message) => {
    const text = message.toLowerCase();
    
    if (text.match(/oi|olá|ola|hey|hi/))
        return automaticResponses.greeting[Math.floor(Math.random() * automaticResponses.greeting.length)];
    
    if (text.match(/serviço|serviços|ajuda|help|faz/))
        return automaticResponses.services[0];
    
    if (text.match(/crypto|cripto|bitcoin|btc/))
        return automaticResponses.crypto[0];
    
    if (text.match(/tecnologia|programação|sistema|automação|bot/))
        return automaticResponses.technology[0];
    
    return automaticResponses.default[0];
};
const qrCodePath = path.join(__dirname, 'qrcode.txt');

// Função para ler o QR Code salvo no arquivo
const getStoredQRCode = () => {
    try {
        if (fs.existsSync(qrCodePath)) {
            const qr = fs.readFileSync(qrCodePath, 'utf-8');
            return qr.trim() !== '' ? qr : null;
        }
    } catch (error) {
        console.error("❌ Erro ao ler QR Code salvo:", error);
    }
    return null;
};

// Função para salvar o QR Code (em base64) na memória e no arquivo
const storeQRCode = (qrCode) => {
    try {
        qrCodeBase64 = qrCode;
        fs.writeFileSync(qrCodePath, qrCode, 'utf-8');
    } catch (error) {
        console.error("❌ Erro ao salvar QR Code:", error);
    }
};

// Função para verificar horário de funcionamento
const isWorkingHours = () => {
    if (!botConfig.workingHoursStart || !botConfig.workingHoursEnd) return true;
    const hour = new Date().getHours();
    return hour >= botConfig.workingHoursStart && hour <= botConfig.workingHoursEnd;
};

// Função para verificar se número está permitido
const isAllowedNumber = (number) => {
    if (number === botConfig.ownerNumber + '@c.us') return true;
    if (botConfig.allowedNumbers.includes(number.replace('@c.us', ''))) return true;
    return false;
};

// Função para verificar se número está banido
const isBannedNumber = (number) => {
    return botConfig.bannedNumbers.includes(number.replace('@c.us', ''));
};

// Função para verificar mensagem por palavras banidas
const hasBannedKeywords = (message) => {
    if (!botConfig.bannedKeywords.length) return false;
    return botConfig.bannedKeywords.some(keyword => 
        message.toLowerCase().includes(keyword.toLowerCase()));
};

/**
 * Rota única para:
 * 1) Verificar se o bot já está conectado.
 * 2) Se não estiver, inicializa o Venom e aguarda o primeiro QR Code.
 * 3) Retorna o QR Code em base64 para o front.
 */
app.get('/get-qrcode', async (req, res) => {
    // Se o bot já foi inicializado, retorna status + QR armazenado (se desejar exibir novamente)
    if (client) {
        return res.json({
            success: true,
            message: 'Bot já conectado',
            qr: getStoredQRCode() || null
        });
    }

    let qrResolved = false;
    let resolveQrPromise = null;
    
    try {
        // Criando a Promise para capturar o QR code antes de iniciar o Venom
        const qrPromise = new Promise((resolve, reject) => {
            resolveQrPromise = resolve;
            
            // Define um timeout para rejeitar a Promise se não receber o QR code
            setTimeout(() => {
                if (!qrResolved) {
                    reject(new Error('Tempo excedido aguardando QR Code'));
                }
            }, 60000); // Aumentado para 60 segundos de timeout
        });
        
        // Inicializa o Venom Bot com função de callback para o QR code
        create({
            session: 'whatsapp-session',
            multidevice: true,
            headless: 'new',
            browserArgs: ['--no-sandbox', '--disable-setuid-sandbox'],
            qrTimeout: 0,
            logQR: true,
            disableWelcome: true,
            catchQR: async (base64Qr, asciiQR, attempt) => {
                console.log('\n==================================');
                console.log(`📱 Novo QR Code gerado! (tentativa ${attempt})`);
                console.log('==================================\n');
                
                try {
                    // Salva o QR code em um arquivo
                    const qrImagePath = path.join(__dirname, 'qrcode.png');
                    await QRCode.toFile(qrImagePath, base64Qr);
                    console.log(`\n✅ QR Code salvo em: ${qrImagePath}`);
                    console.log('\n🔍 Por favor, abra o arquivo qrcode.png para escanear com WhatsApp\n');
                    
                    // Também tenta exibir no terminal como backup
                    console.log('\nQR Code no terminal (caso seja visível):\n');
                    qrcodeTerminal.generate(base64Qr, { small: false });
                    
                    // Adiciona o prefixo necessário para renderização no frontend
                    if (!base64Qr.startsWith('data:')) {
                        base64Qr = 'data:image/png;base64,' + base64Qr;
                    }
                    
                    storeQRCode(base64Qr);
                    qrResolved = true;
                    if (resolveQrPromise) resolveQrPromise(base64Qr);
                    
                    console.log('\n==================================');
                    console.log('⚠️ Se você não conseguir ver o QR code no terminal:');
                    console.log('1. Abra o arquivo qrcode.png gerado na pasta do projeto');
                    console.log('2. Ou acesse http://localhost:5000/get-qrcode no navegador');
                    console.log('==================================\n');
                } catch (err) {
                    console.error("❌ Erro ao processar QR code:", err);
                }
            }
        })
        .then((bot) => {
            client = bot; // Salva o cliente para uso posterior
            console.log('✅ Bot iniciado com sucesso!');

            // Configurar listener para mensagens recebidas
            client.onMessage(async (message) => {
                if (botConfig.ignoreGroups && message.isGroupMsg) return;
                if (isBannedNumber(message.from)) return;
                if (!isWorkingHours()) return;
                if (hasBannedKeywords(message.body)) return;
                
                try {
                    const messageText = message.body.toLowerCase().trim();

                    // Verifica comandos de ativação/desativação
                    if (messageText === "baymax") {
                        const status = toggleAutoReply(message.from, true);
                        await client.sendText(message.from, 
                            "Olá! Estou ativo e pronto para ajudar! 🤖\nPosso responder suas mensagens normalmente agora.");
                        return;
                    } else if (messageText === "ok baymax") {
                        const status = toggleAutoReply(message.from, false);
                        await client.sendText(message.from, 
                            "Entendi! Vou ficar em modo de espera. 😴\nAgora só responderei a comandos que começam com '!'");
                        return;
                    }

                    // Adiciona delay se configurado
                    if (botConfig.responseDelay > 0) {
                        await new Promise(resolve => setTimeout(resolve, botConfig.responseDelay));
                    }

                    let response;
                    let imageUrl = null;

                    // Verifica se a mensagem contém uma imagem
                    if (message.isMedia && message.type === 'image') {
                        const imagePath = await downloadImage(message);
                        if (imagePath) {
                            const imageBuffer = fs.readFileSync(imagePath);
                            imageUrl = `data:image/jpeg;base64,${imageBuffer.toString('base64')}`;
                            fs.unlinkSync(imagePath);
                        }
                    }

                    // Processa a mensagem com IA apenas se autoReply estiver ativado para este número
                    if (isAutoReplyEnabled(message.from) || message.body.startsWith('!')) {
                        response = await handleMessage(message.body, message.from, conversationContext.get(message.from));
                        await client.sendText(message.from, response);
                        if (botConfig.debugMode) {
                            console.log('Resposta:', response);
                        }
                    }
                } catch (error) {
                    console.error("❌ Erro ao processar mensagem:", error);
                    if (isAllowedNumber(message.from)) {
                        await client.sendText(message.from, 
                            "Desculpe, tive um problema ao processar sua mensagem. Pode tentar novamente?");
                    }
                }
            });
        })
        .catch(err => {
            console.error('❌ Erro ao iniciar o bot:', err);
        });

        // Aguarda o QR code ser gerado e capturado
        const qrcode = await qrPromise;

        // Retorna o QR Code para o front-end
        return res.json({
            success: true,
            message: 'QR Code gerado com sucesso',
            qr: qrcode
        });
    } catch (error) {
        console.error("❌ Erro ao gerar QR Code:", error);
        return res.status(500).json({
            success: false,
            error: 'Erro ao gerar QR Code',
            details: error.message
        });
    }
});

// Rota para verificar o status do bot
app.get('/status', (req, res) => {
    if (client) {
        return res.json({ success: true, status: 'Bot está rodando' });
    }
    return res.json({
        success: false,
        status: 'Bot não iniciado',
        qr: getStoredQRCode()
    });
});

// Rota para enviar mensagens
app.post('/send-message', async (req, res) => {
    if (!client) {
        return res.status(500).json({ success: false, error: 'Bot não iniciado' });
    }
    const { number, message } = req.body;
    if (!number || !message) {
        return res.status(400).json({ success: false, error: 'Número e mensagem são obrigatórios' });
    }

    const formattedNumber = `${number}@c.us`;
    try {
        console.log(`📤 Enviando mensagem para ${formattedNumber}: ${message}`);
        const result = await client.sendText(formattedNumber, message);
        console.log("✅ Mensagem enviada com sucesso!", result);
        return res.json({ success: true, message: 'Mensagem enviada!' });
    } catch (error) {
        console.error("❌ Erro ao enviar mensagem:", error);
        return res.status(500).json({
            success: false,
            error: 'Erro ao enviar mensagem',
            details: error
        });
    }
});

// Inicia o servidor na porta 5000
const PORT = 5000;
app.listen(PORT, () => {
    console.log(`🚀 Servidor rodando na porta ${PORT}`);
});
