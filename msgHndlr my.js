const { decryptMedia } = require('@open-wa/wa-decrypt');
const fs = require('fs-extra');
const axios = require('axios');
const moment = require('moment-timezone');
const color = require('./lib/color');
const { help } = require('./lib/help');
const path = require('path');

const http = require('http');
const https = require('https');
const urlParse = require('url').parse;

const googleTTS = require('google-tts-api'); // CommonJS

const dialogflow = require('dialogflow');
const config = require('./config');

moment.tz.setDefault('America/Sao_Paulo').locale('pt-br');

const credentials = {
	client_email: config.GOOGLE_CLIENT_EMAIL,
	private_key: config.GOOGLE_PRIVATE_KEY,
};

const sessionClient = new dialogflow.SessionsClient({
	projectId: config.GOOGLE_PROJECT_ID,
	credentials,
});

const bannedUsers = [
	'<EMAIL>', // <PERSON>rran
];
const silenceBannedUsers = [
	// '<EMAIL>', // Code Monkey
	// '<EMAIL>' // Grupo dos bots
]

/**
 * Send a query to the dialogflow agent, and return the query result.
 * @param {string} projectId The project to be used
 */
async function sendToDialogFlow(msg, session, params) {
	let textToDialogFlow = msg;
	try {
		const sessionPath = sessionClient.sessionPath(config.GOOGLE_PROJECT_ID, session);

		const request = {
			session: sessionPath,
			queryInput: {
				text: {
					text: textToDialogFlow,
					languageCode: config.DF_LANGUAGE_CODE,
				},
			},
			queryParams: {
				payload: {
					data: params,
				},
			},
		};

		const responses = await sessionClient.detectIntent(request);
		const result = responses[0].queryResult;
		console.log('INTENT ENCONTRADO: ', result.intent.displayName);
		let defaultResponses = [];

		if (result.action !== 'input.unknown') {
			result.fulfillmentMessages.forEach((element) => {
				defaultResponses.push(element);
			});
		}

		if (defaultResponses.length === 0) {
			result.fulfillmentMessages.forEach((element) => {
				if (element.platform === 'PLATFORM_UNSPECIFIED') {
					defaultResponses.push(element);
				}
			});
		}

		result.fulfillmentMessages = defaultResponses;

		//console.log("se enviara el resultado: ", result);

		return result;
	} catch (e) {
		console.log('error');
		console.log(e);
	}
}

module.exports = msgHandler = async (client, message) => {
	try {
		const { urlParametro, type, id, from, t, sender, isGroupMsg, chat, caption, isMedia, mimetype, quotedMsg, quotedMsgObj, mentionedJidList } = message;
		let { body } = message;
		const { name, formattedTitle } = chat;
		let { pushname, verifiedName } = sender;
		pushname = pushname || verifiedName;
		const commands = caption || body || '';
		const falas = commands.toLowerCase();
		const command = commands.toLowerCase().split(' ')[0] || '';
		const args = commands.split(' ');

		if (silenceBannedUsers.includes(chat.id)) {
			return;
		}
		
		console.log('----------------------------------------');
		const msgs = (message) => {
			if (command.startsWith('!')) {
				if (message.length >= 10) {
					return `${message.substr(0, 15)}`;
				} else {
					return `${message}`;
				}
			}
		};

		const mess = {
			wait: '⏳ Fazendo figurinha...',
			error: {
				St: '[❗] Envie uma imagem com uma legenda *!s* ou marque a imagem que já foi enviada',
			},
		};

		const time = moment(t * 1000).format('DD/MM HH:mm:ss');
		const botNumber = await client.getHostNumber();
		const blockNumber = await client.getBlockedIds();
		const groupId = isGroupMsg ? chat.groupMetadata.id : '';
		const groupAdmins = isGroupMsg ? await client.getGroupAdmins(groupId) : '';
		const isGroupAdmins = isGroupMsg ? groupAdmins.includes(sender.id) : false;
		const isBotGroupAdmins = isGroupMsg ? groupAdmins.includes(botNumber + '@c.us') : false;
		const ownerNumber = ['+<EMAIL>', '+5521999222644']; // replace with your whatsapp number
		const isOwner = ownerNumber.includes(sender.id);
		const isBlocked = blockNumber.includes(sender.id);
		const uaOverride =
			'WhatsApp/2.2029.4 Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_5) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.116 Safari/537.36';
		const isUrl = new RegExp(/https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b([-a-zA-Z0-9()@:%_+.~#?&/=]*)/gi);
		if (!isGroupMsg && command.startsWith('!'))
			console.log('\x1b[1;31m~\x1b[1;37m>', '[\x1b[1;32mEXEC\x1b[1;37m]', time, color(msgs(command)), 'from', color(pushname));
		if (isGroupMsg && command.startsWith('!'))
			console.log(
				'\x1b[1;31m~\x1b[1;37m>',
				'[\x1b[1;32mEXEC\x1b[1;37m]',
				time,
				color(msgs(command)),
				'from',
				color(pushname),
				'in',
				color(formattedTitle)
			);
		//if (!isGroupMsg && !command.startsWith('!')) console.log('\x1b[1;33m~\x1b[1;37m>', '[\x1b[1;31mMSG\x1b[1;37m]', time, color(body), 'from', color(pushname))
		
		if (isGroupMsg && !command.startsWith('!'))
				console.log('\x1b[1;33m~\x1b[1;37m>', '[\x1b[1;31mMSG\x1b[1;37m]', time, color(body), 'from', color(pushname), 'in', color(formattedTitle));
		if (isBlocked) return;
		//if (!isOwner) return

		
		console.log('FROM 		===>', color(pushname));
		console.log('FROM_ID 	===>', chat.id);
		console.log('ARGUMENTOS	===>', color(args));
		console.log('FALAS 		===>', color(falas));
		console.log('COMANDO 	===>', color(command));
		
		if (command.startsWith('!') && bannedUsers.includes(chat.id)) {
			await client.sendText(from, '*_Você foi banido, não pode usar o bot. :(_*', id);
			console.log("USUÁRIO BANIDO!");
			return;
		}
		let objeto = JSON.parse(await fs.readFileSync('./lib/dialogflowActive.json', { encoding: 'utf8', flag: 'r' }));

		if (objeto?.ativo == 'true') {
			const payload = await sendToDialogFlow(falas, from, 'params');
			const responses = payload?.fulfillmentMessages;

			console.log('RECEBEU DIALOGFLOW ======>', payload);
			for (const response of responses) {
				let randomIndex = Math.floor(Math.random() * response?.text?.text.length);
				await client.reply(from, `${response?.text?.text[randomIndex]}`, id);
			}
		}
		
		switch (falas) {
			case 'me ajuda bot':
			case 'bot me ajuda':
				await client.sendText(from, help);
				break;

			case 'oi bot':
				await client.reply(from, 'Fala? que ta pegando? sei fazer algumas coisas, digite: *!ajuda*', id);
				break;
		}

		switch (command) {
			case '!pix':
				await client.sendText(from, 'Minha chave pix é o meu email:');
				await client.sendText(from, '<EMAIL>');
				break;
			case '!concursos':
			case '!concurso':
				if (args.length === 1) return client.reply(from, 'Preciso de um estado para localizar os concursos...', id);

				let request = await axios.get(
					`https://especiais.g1.globo.com/economia/concursos-e-emprego/lista-de-concursos-publicos-e-vagas-de-emprego/data/data.json`
				);
				let cidadeConcurso = body.split('.');
				let concursos = request?.data?.docs;

				encontrado = ``;
				quantidade = 0;
				console.log(concursos);

				concursos.forEach(async (data) => {
					if (String(data?.estado.toLowerCase()) == String(cidadeConcurso[1].toLowerCase())) {
						quantidade++;
						encontrado += `\n*Status*: ${data?.tipo}\n*Instituicao:* ${data?.instituicao}\n*Inicio:* ${
							data?.inicio ? data?.inicio + '/' : 'Sem previsão'
						} *Fim:* ${data?.encerramento}\n*Vagas:* ${data?.vagas}\n*Salário:* ${data?.salario}\n*Escolaridade:* ${data.escolaridade}\n*Local:* ${
							data.local
						} / *Estado:* ${data.estado}\n*Link:* ${data.link}\n-------\n`;
					}
				});

				await client.reply(from, `Pera ai, procurei no G1 e encontrei ${quantidade} concursos...`, id);
				setTimeout(() => client.reply(from, `${encontrado}`, id), 5000);

				break;

			case '!tts':
			case 'tts!':
				if (args.length === 1) return client.reply(from, 'Como eu vou adivinhar o devo buscar?', id);
				let string = body.split(' ').slice(1).join(' ');
				console.log('TTS STRING => ', string);
				if (string.length >= 200) {
					client.reply(from, `Porra bicho q treco grande, quer me bugar??`, id);
					break;
				}
				url = await googleTTS.getAudioUrl(`${string}`, {
					lang: 'pt_BR',
					slow: false,
					host: 'https://translate.google.com',
				});

				const dest = await path.resolve(__dirname, './media/to/translate.mp3'); // file destination
				await downloadFile(url, dest);
				await client.sendFile(from, './media/to/translate.mp3', 'translate', 'AAAAAAAAAUHHH', id);
				break;

			case '!horoscopo':
			case '!horóscopo':
				if (args.length === 1) return client.reply(from, 'Como eu vou adivinhar o horoscopo?', id);
				await client.reply(from, 'Buscando o horoscopo... pera um pouco', id);

				let horoscopo = await axios.get(`https://horoscopefree.herokuapp.com/daily/pt/`);
				const { publish, language, aries, taurus, gemini, cancer, leo, scorpio, libra, sagittarius, capricorn, aquarius, pisces, virgo } =
					horoscopo.data;
				switch (args[1]) {
					case 'aries':
						await client.sendText(from, `${aries}`);
						break;
					case 'touro':
						await client.sendText(from, `${taurus}`);
						break;
					case 'gemios':
					case 'gêmios':
						await client.sendText(from, `${gemini}`);
						break;
					case 'cancer':
					case 'câncer':
						await client.sendText(from, `${cancer}`);
						break;
					case 'leao':
					case 'leão':
						await client.sendText(from, `${leo}`);
						break;
					case 'escorpiao':
					case 'escorpião':
						await client.sendText(from, `${scorpio}`);
						break;
					case 'libra':
						await client.sendText(from, `${libra}`);
						break;
					case 'sagitario':
					case 'sagitário':
						await client.sendText(from, `${sagittarius}`);
						break;
					case 'capricornio':
						await client.sendText(from, `${capricorn}`);
						break;
					case 'aquario':
					case 'aquário':
						await client.sendText(from, `${aquarius}`);
						break;
					case 'peixes':
						await client.sendText(from, `${pisces}`);
					case 'virgem':
						await client.sendText(from, `${virgo}`);
						break;
					default:
						await client.sendText(from, `Não encontrei nada...`);
				}
				break;


			case '!buscamemes':
			case '!buscameme':
				await client.reply(from, `Vasculhando a internet... pera um pouco`, id);

				let meme = await axios.get(`https://api.imgflip.com/get_memes`);

				myArray = [];
				meme?.data?.data?.memes.forEach(async (data, index) => {
					myArray.push({ url: data?.url, id: data?.id, name: data?.name });
					myArray = myArray.sort(() => Math.random() - 0.5);
				});

				myArray.forEach(async (data, index) => {
					urlRandom = myArray[Math.floor(Math.random() * myArray.length)];
					if (index < 6) {
						await client.sendImage(from, `${urlRandom?.url}`, `bot do Kauã`, `*ID:* ${urlRandom?.id}\n*REF:* ${urlRandom?.name}`);
					}
				});

				break;

			case '!escrevememe':
				if (args.length === 1)
					return client.reply(from, 'Preciso de 2 textos e o ID da imagem para montar o meme... procure uma imagem !buscameme', id);

				let queryMeme = body.split('.');
				if (queryMeme.length <= 3) return client.reply(from, 'Preciso de todos os parametros para montar o meme', id);

				if (queryMeme[1].length == 0) return client.reply(from, 'Preciso do texto 1...', id);
				if (queryMeme[2].length == 0) return client.reply(from, 'Preciso do texto 2...', id);
				if (queryMeme[3].length == 0 && queryMeme[3].length <= 3) return client.reply(from, 'Preciso de um ID...', id);

				let text0 = queryMeme[1] ?? 'Como eu vou adivinhar';
				let text1 = queryMeme[2] ?? 'O que devo escrever?';
				let text2 = queryMeme[3] ?? '91545132';

				let dataSend = `text0=${encodeURIComponent(text0)}&text1=${encodeURIComponent(text1)}&template_id=${text2}&username=${encodeURIComponent(
					'kaualandi'
				)}&password=${encodeURIComponent('sdVKRA2QZm9fQx!')}`;
				let makeMeme = await axios({
					method: 'post',
					url: 'https://api.imgflip.com/caption_image',
					data: dataSend,
					headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
				});

				if (makeMeme?.data?.success != true) return client.reply(from, `${makeMeme?.data?.error_message}`, id);
				await client.sendImage(
					from,
					`${makeMeme?.data?.data?.url}`,
					`bot do Kauã`,
					`Pronto, meme gerado com sucesso. você pode visualizar ele aqui nesse site ${makeMeme?.data?.data?.page_url}`
				);

				break;

			case '!clima':
				if (args.length === 1) return client.reply(from, 'Ainda não adivinho coisas... preciso saber a cidade também', id);

				if (typeof args[1] == 'undefined') {
					return await client.reply(from, `Coloca um . antes da cidade`, id);
				}

				
				if (!body.includes('.')) {
					return await client.reply(from, `Coloca um . antes da cidade`, id);
				}
				let cidade = body.split('.');
				console.log(typeof cidade[1]);

				if (typeof cidade[1] !== 'undefined') {
					if (cidade[1].length == 0) return client.reply(from, 'Preciso de uma cidade...', id);

					await client.reply(from, `Verificando com São Pedro como está o clima em ${cidade[1]}... pera um pouco`, id);

					let clima = await axios.get(`https://weather.contrateumdev.com.br/api/weather/city/?city=${encodeURI(cidade[1])}`);

					if (clima?.data?.cod == '404') return await client.reply(from, `Uai... ${clima?.data?.message}`, id);

					await client.sendText(
						from,
						`*Temperatura:* ${clima?.data?.main?.temp} ºC \n*Sensação térmica:* ${clima?.data?.main?.feels_like} ºC \n*Temperatura mínima:* ${clima?.data?.main?.temp_min} ºC \n*Temperatura máxima:* ${clima?.data?.main?.temp_max} ºC \n*Pressão atmosférica:* ${clima?.data?.main?.pressure}\n*Umidade:* ${clima?.data?.main?.humidity}%
----------------------\n${clima?.data?.name} - lat: ${clima?.data?.coord?.lat} lon: ${clima?.data?.coord?.lon}
                `
					);
				} else {
					return client.reply(from, 'Preciso de uma cidade...', id);
				}

				break;

			case '!cep':
				if (args.length === 1) return client.reply(from, 'Como eu vou adivinhar o cep?', id);

				let response = await axios.get(`https://viacep.com.br/ws/${args[1]}/json/`);
				const { logradouro, bairro, localidade, siafi, ibge } = response.data;

				await client.reply(from, 'Buscando o CEP... pera um pouco', id);
				await client.sendText(from, `🌎️ Rua: ${logradouro}, ${bairro}, ${localidade}\nSiafi: ${siafi}, Ibge: ${ibge} `);

				break;

			case '!meunumero':
				let chatNumber = sender.id.split('-');
				let ddd = chatNumber[0].substring(2, 4);
				let number = chatNumber[0].substring(4, 12);

				client.reply(from, `Seu numero é: *${number}* seu ddd é: *${ddd}*`, id);

				break;

			case '!sticker':
			case '!stiker':
			case '!s':
				if (isMedia && type === 'image') {
					const mediaData = await decryptMedia(message, uaOverride);
					const imageBase64 = `data:${mimetype};base64,${mediaData.toString('base64')}`;
					await client.sendImageAsSticker(from, imageBase64, { author: 'Bot do Kauã', pack: 'PackDoBot', keepScale: true });
				} else if (quotedMsg && quotedMsg.type == 'image') {
					const mediaData = await decryptMedia(quotedMsg, uaOverride);
					const imageBase64 = `data:${quotedMsg.mimetype};base64,${mediaData.toString('base64')}`;
					await client.sendImageAsSticker(from, imageBase64, { author: 'Bot do Kauã', pack: 'PackDoBot', keepScale: true });
				} else if (args.length === 2) {
					const url = args[1];
					if (url.match(isUrl)) {
						await client.sendStickerfromUrl(from, url, { method: 'get' }).catch((err) => console.log('Caught exception: ', err));
					} else {
						client.reply(from, mess.error.Iv, id);
					}
				} else {
					client.reply(from, mess.error.St, id);
				}
				break;
			case '!stickergif':
			case '!stikergif':
			case '!sg':
			case '!sgif':
				if (isMedia) {
					if ((mimetype === 'video/mp4' && message.duration < 30) || (mimetype === 'image/gif' && message.duration < 30)) {
						const mediaData = await decryptMedia(message, uaOverride);
						client.reply(from, '⏳ Fazendo figurinha...', id);

						await client.sendMp4AsSticker(from, `data:${mimetype};base64,${mediaData.toString('base64')}`, null, {
							stickerMetadata: true,
							author: 'Bot do Kauã',
							pack: 'PackDoBot',
							fps: 10,
							square: '512',
							loop: 0,
						});
					} else client.reply(from, 'Envie o gif com a legenda *!sg* máx. 30 segundos!', id);
				}
				break;

			case '!ajuda':
			case '!menu':
			case '!help':
				// await client.sendButtons(
					// 	from,
					// 	'Esse menu foi ativado, por que você falou o meu nome, em que posso ser útil?',
					// 	[
					// 		{
					// 			id: 'id1',
					// 			text: 'Menu do bot',
					// 		},
					// 		{
					// 			id: 'id2',
					// 			text: 'Quem sou eu?',
					// 		},
					// 		{
					// 			id: 'id3',
					// 			text: 'Nada, obrigado.',
					// 		},
					// 	],
					// 	'Oi? ta falando de mim? Em que posso te ajudar?'
					// );
				await client.sendText(from, help);
				let batteryLevel = await client.getBatteryLevel();
				let isPlugged = await client.getIsPlugged(from);
				let connectionState = await client.getConnectionState();

				await client.reply(
					from,
					`----------------------\n*Status*: ${connectionState}\n*Carregando*: ${
						isPlugged ? 'Sim' : 'Não'
					}\n----------------------`,
					id
				);
				break;

			case '':
				break;

			case '!aniversário':
			case '!aniversario':
				if (args.length === 1) {
					client.reply(from, 'Como eu vou adivinhar a data? Mande no formato DD/MM/YYYY', id);
				} else {
					let date = args[1].split('/');
					let day = date[0];
					let month = date[1];
					let year = date[2];
					if (isNaN(day) || isNaN(month) || isNaN(year)) {
						client.reply(from, 'Essa data tá errada fiote. Mande no formato DD/MM/YYYY', id);
					} else {
						day = parseInt(day);
						month = parseInt(month);
						year = parseInt(year);
						let date = new Date(year, month - 1, day);
						let today = new Date();
						let diff = date.getTime() - today.getTime();
						if (diff < 0) return client.reply(from, 'Essa data já passou, lembre-se de colocar o ano do próximo aniversário!', id);
						let days = Math.floor(diff / (1000 * 60 * 60 * 24));
						let hours = Math.floor((diff % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
						let minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
						let seconds = Math.floor((diff % (1000 * 60)) / 1000);
						let message = `Faltam ${days} dias, ${hours} horas, ${minutes} minutos e ${seconds} segundos para o aniversário!`;
						client.reply(from, message, id);
					}
				}
				break;
				
			case '!moeda':
			case '!converter':
			case '!cot':
			case '!cotacao':
				if (args.length === 1) return client.reply(from, 'Digite !converter .BTCxUSD', id);
				let parametro = body.split('.');
				let moeda = parametro[1];

				parametroBusca = moeda.split('x');

				try {
					console.log(parametroBusca[0]);
					console.log(parametroBusca[1]);

					console.error(`https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest?symbol=${parametroBusca[0]}&convert=${parametroBusca[1]}`);

					let coinmarketcap = await axios({
						method: 'GET',
						url: `https://pro-api.coinmarketcap.com/v1/cryptocurrency/quotes/latest?symbol=${parametroBusca[0]}&convert=${parametroBusca[1]}`,
						headers: { 'Content-Type': 'application/json', 'X-CMC_PRO_API_KEY': 'b2776f73-fbda-4b91-8d8b-221be52eb5ff' },
					});

					let coinmarketcapData = coinmarketcap?.data?.data;

					let textoSend = `*Nome:* ${coinmarketcapData[parametroBusca[0]].name}\n*Ranking:* ${
						coinmarketcapData[parametroBusca[0]].cmc_rank != null ? coinmarketcapData[parametroBusca[0]].cmc_rank : 'Sem posição'
					}\n*Sigla:* ${coinmarketcapData[parametroBusca[0]].symbol}\n*Preço:* ${parseFloat(
						coinmarketcapData[parametroBusca[0]].quote[parametroBusca[1]].price
					).toLocaleString('pt-br', { style: 'currency', currency: `${parametroBusca[1]}` })}\n*Volume 24h:* ${parseFloat(
						coinmarketcapData[parametroBusca[0]].quote[parametroBusca[1]].volume_24h
					).toLocaleString('pt-br', { style: 'currency', currency: `${parametroBusca[1]}` })}\n*Suprimento máximo:* ${
						coinmarketcapData[parametroBusca[0]].max_supply != null
							? parseFloat(coinmarketcapData[parametroBusca[0]].max_supply).toLocaleString('pt-br', {
									style: 'currency',
									currency: `${parametroBusca[1]}`,
							  })
							: 'R$ 0,00'
					}\n*Suprimento circulante:* ${parseFloat(coinmarketcapData[parametroBusca[0]].circulating_supply).toLocaleString('pt-br', {
						style: 'currency',
						currency: `${parametroBusca[1]}`,
					})}\n*Suprimento total:* ${parseFloat(coinmarketcapData[parametroBusca[0]].total_supply).toLocaleString('pt-br', {
						style: 'currency',
						currency: `${parametroBusca[1]}`,
					})}\n*Atualização:* ${coinmarketcapData[parametroBusca[0]].quote[parametroBusca[1]]?.last_updated}\n`;

					await client.reply(from, `${textoSend}`, id);
				} catch (error) {
					console.error(error);
					await client.reply(from, `Não achei essa moeda... *${parametroBusca[0]}*, cuidado ao investir!`, id);
				}

				break;
		}
	} catch (err) {
		await client.sendText(`Puts, deu merda... Erro: ${err}`);

		console.log(color('[ERROR]', 'red'), err);
		client.kill().then((a) => console.log(a));
	}
};

function downloadFile(url, dest) {
	return new Promise((resolve, reject) => {
		const info = urlParse(url);
		const httpClient = info.protocol === 'https:' ? https : http;
		const options = {
			host: info.host,
			path: info.path,
			headers: {
				'user-agent': 'WHAT_EVER',
			},
		};

		httpClient
			.get(options, (res) => {
				// check status code
				if (res.statusCode !== 200) {
					const msg = `request to ${url} failed, status code = ${res.statusCode} (${res.statusMessage})`;
					reject(new Error(msg));
					return;
				}

				const file = fs.createWriteStream(dest);
				file.on('finish', function () {
					// close() is async, call resolve after close completes.
					file.close(resolve);
				});
				file.on('error', function (err) {
					// Delete the file async. (But we don't check the result)
					fs.unlink(dest);
					reject(err);
				});

				res.pipe(file);
			})
			.on('error', reject)
			.end();
	});
}
