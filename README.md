# Baymax - Assistente WhatsApp

Baymax é um bot assistente para WhatsApp que oferece diversos serviços e funcionalidades através de uma interface conversacional natural.

## 🤖 Como Usar

### Ativação e Desativação

Para ativar o bot:
```
Você: baymax
Bot: Oi! Tudo bem? 😊
[Mostra menu de opções]
```

Para desativar:
```
Você: ok baymax
Bot: Blz! Vou ficar quietinho por aqui 😴
Se precisar é só me chamar!
```

## 💻 Serviços Disponíveis

### 1. Desenvolvimento e Automação
- Automação de WhatsApp
- Desenvolvimento de sistemas
- Integração com IA
- Criação de bots

### 2. Criação de Figurinhas 🎯

#### Figurinha Normal
```
Método 1:
Você: baymax
Bot: [mostra menu]
Você: 5
Bot: Me manda a imagem que você quer transformar em figurinha! 😊
[Envie a imagem]

Método 2:
[Envie uma imagem com legenda "!s"]
```

#### Figurinha Animada (GIF)
```
Método 1:
Você: baymax
Bot: [mostra menu]
Você: 6
Bot: Me manda o GIF que você quer transformar em figurinha! 😊
[Envie o GIF]

Método 2:
[Envie um GIF com legenda "!sg"]

Obs: Limite de 30 segundos para GIFs
```

### 3. Consultas 🔍

#### Consulta de CEP
```
Método 1:
Você: baymax
Bot: [mostra menu]
Você: 7
Bot: Me fala o CEP que você quer consultar 🔍
Você: 20021290
Bot: 🌎️ Rua: Rua exemplo, Bairro exemplo, Cidade
     Siafi: 1234, Ibge: 5678

Método 2:
Você: !cep 20021290
```

#### Consulta de Clima
```
Método 1:
Você: baymax
Bot: [mostra menu]
Você: 8
Bot: Qual cidade você quer saber a previsão do tempo? 🌤️
Você: Rio de Janeiro
[Bot mostra previsão]

Método 2:
Você: !clima Rio de Janeiro
```

#### Outras Consultas
- CPF: `!cpf [número]`
- Nome: `!nome .[nome]`
- Telefone: `!telefone [número]`
- Concursos: `!concursos .[estado]`

### 4. Ferramentas de Mídia 🎨

#### Criação de Memes
```
1. Buscar templates:
Você: !buscameme
[Bot envia 6 templates aleatórios com IDs]

2. Criar meme:
Você: !escrevememe .texto1 .texto2 .ID
[Bot gera o meme com os textos]
```

#### Conversão de Texto em Áudio
```
Você: !tts [texto]
[Bot envia áudio com o texto]
```

### 5. Utilitários 🛠️

#### Informações do Número
```
Você: !meunumero
Bot: Seu numero é: *999887766* seu ddd é: *21*
```

#### Horóscopo
```
Você: !horoscopo [signo]
[Bot mostra horóscopo do dia]
```

## 🎮 Comandos de Grupo

### Administração
- `!ban @usuário` - Bane usuário do grupo
- `!add [número]` - Adiciona número ao grupo
- `!kick @usuário` - Remove usuário do grupo
- `!promote @usuário` - Promove usuário a admin
- `!demote @usuário` - Remove admin de usuário
- `!linkgroup` - Obtém link do grupo
- `!apagar` - Apaga mensagem do bot (responda à mensagem)

### Sistema de Votação
```
!voteban @usuário - Inicia votação para banir
!unvoteban @usuário - Remove voto de ban
```

## 🤖 Recursos de IA

O bot usa LLaMA para processamento de linguagem natural, permitindo:
- Conversas naturais
- Entendimento de contexto
- Respostas personalizadas
- Processamento de comandos flexível

## ⚙️ Configurações

O bot pode ser configurado através de variáveis de ambiente:
- `BOT_NAME` - Nome do bot (padrão: "Baymax")
- `OWNER_NUMBER` - Número do proprietário
- `AUTO_REPLY` - Ativar/desativar respostas automáticas
- `WORKING_HOURS_START` - Hora de início do funcionamento
- `WORKING_HOURS_END` - Hora de fim do funcionamento
- `IGNORE_GROUPS` - Ignorar mensagens de grupos
- `RESPONSE_DELAY` - Delay entre mensagens
- `MAX_MESSAGES_PER_MINUTE` - Limite de mensagens por minuto
- `MENU_TIMEOUT` - Tempo limite do menu (padrão: 5 minutos)

## 🔒 Segurança

O bot inclui recursos de segurança como:
- Lista de usuários banidos
- Lista de grupos silenciados
- Proteção contra spam
- Verificação de administradores
- Controle de acesso por número

## 📱 Compatibilidade

- WhatsApp Web/Desktop
- Multi-device support
- Funciona em grupos e conversas privadas

## 🚀 Desenvolvimento

O bot é desenvolvido em Node.js e usa:
- OpenRouter API para IA
- Venom-bot para WhatsApp
- Axios para requisições
- Moment.js para datas
- QRCode para geração de códigos QR

## 🚀 Instalação

```bash
# Clonar o repositório
git clone https://github.com/dougdotcon/baymax-bot.git

# Entrar no diretório
cd baymax-bot

# Instalar dependências
npm install

# Iniciar o bot
npm start
```

## 🔧 Configuração

1. Renomeie o arquivo `.env.example` para `.env`
2. Preencha as variáveis de ambiente necessárias
3. Execute o bot e escaneie o QR Code com WhatsApp

## 👨‍💻 Desenvolvido por

[@dougdotcon](https://github.com/dougdotcon)

## 📄 Licença

Este projeto está sob a licença MIT. Veja o arquivo [LICENSE](LICENSE) para mais detalhes.
