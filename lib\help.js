const showAll = `*Quer ver mais opções?*
Me fala o que você precisa! 😊`;

function help() {
	return `
O<PERSON>! Tudo bem? 👋
Sou o Baymax, assistente pessoal do Douglas.

*O que eu sei fazer:*

💻 *Desenvolvimento*
1. Automação de WhatsApp
2. Desenvolvimento de sistemas
3. Integração com IA
4. Criação de bots

🛠️ *Ferramentas*
5. Criar figurinhas
6. Figurinhas animadas
7. Consultar CEP
8. Ver previsão do tempo

🤖 *Como me usar:*
• Fale "baymax" para me ativar
• Me conte o que precisa ou escolha um número
• Diga "ok baymax" quando terminar

Pode me perguntar qualquer coisa! 
Vou tentar ajudar da melhor forma possível 😊`;
}

function helpServices() {
	return `
*Serviços Disponíveis* 🛠️

1. *Automação de WhatsApp*
   • Bots personalizados
   • Respostas automáticas
   • Integração com sistemas

2. *Desenvolvimento de Sistemas*
   • Aplicações web/mobile
   • APIs e integrações
   • Sistemas sob demanda

3. *Integração com IA*
   • Chatbots inteligentes
   • Processamento de linguagem
   • Automação com IA

4. *Criação de Bots*
   • Bots para Discord/Telegram
   • Automação de tarefas
   • Bots personalizados

Digite o número para saber mais! 😊`;
}

function helpTech() {
	return `
*Tecnologias* 💻

1. *Desenvolvimento Web*
   • Frontend (React, Vue)
   • Backend (Node.js, Python)
   • Banco de dados

2. *Automação*
   • Scripts personalizados
   • RPA (Automação de processos)
   • Integração de sistemas

3. *Inteligência Artificial*
   • Machine Learning
   • Processamento de linguagem
   • Chatbots avançados

4. *APIs e Integrações*
   • REST/GraphQL
   • Webhooks
   • Microsserviços

Me diz qual área te interessa! 🤓`;
}

const helpers = {
	help: help(),
	helpAudios: helpAudios(),
	helpFigurinhas: helpFigurinhas(),
	helpPapo: helpPapo(),
	helpOutros: helpOutros(),
	helpGrupos: helpGrupos(),
	helpConsultas: helpConsultas(),
	readme: readme()
}

function helpAudios() {
	return `
*=== Áudios do BOT! ===*

▫️ toca o berrante
▫️ trem bala
▫️ bom dia
▫️ acorda
▫️ acorda corno
▫️ vamos acordar

${showAll}`;
}

function helpFigurinhas() {
	return `
*=== Figurinhas do BOT! ===*

▫️ Figurinha comum:
  Mande uma foto e digite _>s_ na legenda
▫️ Figurinha animada:
  Mande um gif e digite _>sg_ na legenda

${showAll}`;
}

function helpPapo() {
	return `
*=== Bater-papo do BOT! ===*

▫️ sextou
▫️ bom dia bot
▫️ boa tarde bot
▫️ boa noite bot
▫️ fala bot
▫️ que dia é hoje

${showAll}`;
}

function helpOutros() {
	return `
*=== Outros comandos do BOT! ===*

▫️ >concursos .seu estado
▫️ >cep cep
▫️ >horoscopo seu signo
▫️ >clima .sua cidade
▫️ >buscameme
▫️ >escrevememe .texto1 .texto2 .id da imagem
▫️ >tts isso converte texto em audio
▫️ >meunumero
▫️ >aniversário DD/MM/AAAA (o ano deve ser o do próximo aniversário)
▫️ >converter .BTCxUSD

${showAll}`;
}

function helpGrupos() {
	const unused = `▫️ !adicionar 55219********`;
	return `
*=== Comandos para grupos ===*

▫️ >adminlista
▫️ >donodogrupo
▫️ >mencionartodos
▫️ >ban @usuário
▫️ >promover
▫️ >rebaixar
▫️ >linkdogrupo
▫️ >limpeza

${showAll}`;
}

function helpConsultas() {
	return `
*=== Consultas do BOT! ===*

▫️ >cpf 12312312312
▫️ >nome .nome
▫️ >telefone 11999887744

Você pode mencionar alguém para consultar seu telefone

${showAll}`;
}

function readme() {
	return `
*=== Sobre o Baymax ===*

Sou o Baymax, assistente pessoal do Douglas (@dougdotcon),
especializado em desenvolvimento e automação.

Para me controlar, use:
🟢 "Baymax" - Para me ativar
🔴 "Ok baymax" - Para me desativar

Meu estilo de comunicação é humorístico e técnico.
Sou curioso, engajado e reflexivo.

Posso ajudar com:
🤖 Automação de WhatsApp
💻 Desenvolvimento de sistemas
🧠 Integração com IA
🔧 Criação de extensões e bots

Desenvolvido por @dougdotcon
`;
}

module.exports = {
	help,
	helpServices,
	helpTech,
	showAll,
	helpers
};
