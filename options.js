module.exports = options = (headless, start) => {
    const options = {
        sessionId: 'Baymax-Bot',
        multiDevice: true,
        licenseKey: 'FREE',
        useChrome: true,
        headless: headless,
        authTimeout: 60,
        qrTimeout: 0,
        restartOnCrash: start,
        cacheEnabled: false,
        blockCrashLogs: false,
        disableSpins: true,
        hostNotificationLang: 'PT_BR',
        logConsole: true,
        popup: true,
        defaultViewport: null,
        killProcessOnBrowserClose: true,
        throwErrorOnTosBlock: false,
        chromiumArgs: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--aggressive-cache-discard',
            '--disable-cache',
            '--disable-application-cache',
            '--disable-offline-load-stale-cache',
            '--disk-cache-size=0',
            '--disable-gpu',
            '--disable-dev-shm-usage'
        ]
    }
    return options
}